import re
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import os
import tempfile

# 导入文档处理相关库
import pandas as pd
from docx import Document
from docx.shared import RGBColor
from PyPDF2 import PdfReader, PdfWriter
import io

# 配置
PLACEHOLDER_PATTERN = r'\\[([^\\]]+)\\]'  # 修复：移除额外的反斜杠
GLOBAL_TEMPLATE_CONTENT = ""
TEMPLATE_FILE = ""
OUTPUT_FILE = ""

class DocumentGenerator:
    def __init__(self, root):
        self.root = root
        self.root.title("高级文档生成器")
        self.root.geometry("800x600")
        self.placeholders = []
        self.inputs = {}
        self.template_content = ""
        self.template_file = ""
        self.template_type = ""

        # 初始界面
        self.init_ui()

    def init_ui(self):
        """初始化用户界面"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.root, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # 欢迎标签
        welcome_label = ttk.Label(
            self.main_frame,
            text="文档模板填充工具",
            font=("Arial", 16, "bold")
        )
        welcome_label.pack(pady=20)

        # 选择模板按钮
        select_button = ttk.Button(
            self.main_frame,
            text="选择模板文件",
            command=self.select_template,
            width=20
        )
        select_button.pack(pady=10)

        # 支持格式提示
        format_label = ttk.Label(
            self.main_frame,
            text="支持的格式: TXT, DOCX, XLSX, PDF",
            font=("Arial", 10)
        )
        format_label.pack()

    def select_template(self):
        """允许用户选择模板文件"""
        file_path = filedialog.askopenfilename(
            title="选择模板文件",
            filetypes=(
                ("文本文件", "*.txt"),
                ("Word文档", "*.docx"),
                ("Excel文件", "*.xlsx"),
                ("PDF文件", "*.pdf"),
                ("所有文件", "*.*")
            )
        )

        if not file_path:
            return

        self.template_file = file_path
        self.template_type = os.path.splitext(file_path)[1].lower()

        try:
            self.template_content, self.placeholders = self.load_template(file_path)
            # 清除主框架内容
            for widget in self.main_frame.winfo_children():
                widget.destroy()

            # 创建新的界面展示模板和输入框
            self.create_template_view()
        except Exception as e:
            messagebox.showerror("错误", f"模板加载失败: {str(e)}")

    def load_template(self, file_path):
        """加载模板文件并识别其中的占位符"""
        content = ""
        try:
            if file_path.endswith('.xlsx'):
                # 处理Excel文件
                df = pd.read_excel(file_path, sheet_name=0)
                content = df.to_string(index=False)
                self.template_type = ".xlsx"
            elif file_path.endswith('.docx'):
                # 处理Word文档
                doc = Document(file_path)
                content = '\\n'.join([para.text for para in doc.paragraphs])  # 修复：使用正确的换行符
                self.template_type = ".docx"
            elif file_path.endswith('.pdf'):
                # 处理PDF文件
                reader = PdfReader(file_path)
                content = '\\n'.join([page.extract_text() for page in reader.pages if page.extract_text()])  # 修复：使用正确的换行符
                self.template_type = ".pdf"
            else:
                # 处理普通文本文件
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                self.template_type = ".txt"
        except Exception as e:
            raise Exception(f"模板文件加载失败: {str(e)}")

        # 查找所有占位符
        placeholders = re.findall(PLACEHOLDER_PATTERN, content)

        return content, list(set(placeholders))  # 返回去重后的占位符列表

    def create_template_view(self):
        """创建显示模板和输入表单的界面"""
        # 创建上方的模板预览区和下方的填充区
        self.paned_window = ttk.PanedWindow(self.main_frame, orient=tk.VERTICAL)
        self.paned_window.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 上方区域：模板预览
        preview_frame = ttk.LabelFrame(self.paned_window, text=f"模板预览 ({os.path.basename(self.template_file)})")

        # 添加文本预览框，并高亮显示占位符
        preview_text = scrolledtext.ScrolledText(preview_frame, wrap=tk.WORD, height=10)
        preview_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 插入模板内容并高亮占位符
        preview_text.insert(tk.END, self.template_content)
        preview_text.config(state=tk.DISABLED)  # 设为只读

        # 高亮所有占位符
        for placeholder in self.placeholders:
            pattern = f'[{placeholder}]'
            start_idx = '1.0'
            while True:
                pos = preview_text.search(pattern, start_idx, tk.END)
                if not pos:
                    break
                end_idx = f"{pos}+{len(pattern)}c"
                preview_text.tag_add("highlight", pos, end_idx)
                start_idx = end_idx

        preview_text.tag_config("highlight", background="yellow", foreground="black")

        self.paned_window.add(preview_frame, weight=2)

        # 下方区域：填充表单
        form_frame = ttk.LabelFrame(self.paned_window, text="填充占位符")

        # 创建可滚动的表单区域
        form_canvas = tk.Canvas(form_frame)
        scrollbar = ttk.Scrollbar(form_frame, orient="vertical", command=form_canvas.yview)
        scrollable_frame = ttk.Frame(form_canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: form_canvas.configure(scrollregion=form_canvas.bbox("all"))
        )

        form_canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        form_canvas.configure(yscrollcommand=scrollbar.set)

        form_canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 添加表单字段
        self.inputs = {}

        if not self.placeholders:
            no_placeholders_label = ttk.Label(
                scrollable_frame,
                text="未检测到占位符。请确保模板中包含[占位符]格式的文本。",
                font=("Arial", 10),
                foreground="red"
            )
            no_placeholders_label.pack(pady=10)
        else:
            for i, placeholder in enumerate(self.placeholders):
                field_frame = ttk.Frame(scrollable_frame)
                field_frame.pack(fill=tk.X, padx=5, pady=5)

                # 标签
                label = ttk.Label(field_frame, text=f"{placeholder}:", width=20)
                label.pack(side=tk.LEFT, padx=5)

                # 输入框
                entry = ttk.Entry(field_frame, width=40)
                entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

                # 存储输入框引用
                self.inputs[placeholder] = entry

        self.paned_window.add(form_frame, weight=3)

        # 底部按钮区域
        buttons_frame = ttk.Frame(self.main_frame)
        buttons_frame.pack(fill=tk.X, pady=10)

        # 返回按钮
        back_button = ttk.Button(
            buttons_frame,
            text="返回选择",
            command=self.reset_ui
        )
        back_button.pack(side=tk.LEFT, padx=10)

        # 生成文档按钮
        generate_button = ttk.Button(
            buttons_frame,
            text="生成文档",
            command=self.select_output_format
        )
        generate_button.pack(side=tk.RIGHT, padx=10)

    def reset_ui(self):
        """重置界面回到初始状态"""
        for widget in self.main_frame.winfo_children():
            widget.destroy()
        self.init_ui()

    def select_output_format(self):
        """选择输出文件格式"""
        # 检查输入是否完整
        values = {ph: self.inputs[ph].get() for ph in self.inputs}
        for ph, value in values.items():
            if not value.strip():
                messagebox.showerror("错误", f"请填写 [{ph}]")
                return

        # 创建输出格式选择窗口
        format_window = tk.Toplevel(self.root)
        format_window.title("选择输出格式")
        format_window.geometry("300x200")
        format_window.resizable(False, False)
        format_window.transient(self.root)
        format_window.grab_set()

        # 居中窗口
        format_window.update_idletasks()
        width = format_window.winfo_width()
        height = format_window.winfo_height()
        x = (format_window.winfo_screenwidth() // 2) - (width // 2)
        y = (format_window.winfo_screenheight() // 2) - (height // 2)
        format_window.geometry('{}x{}+{}+{}'.format(width, height, x, y))

        # 标题
        ttk.Label(
            format_window,
            text="选择输出文档格式",
            font=("Arial", 12, "bold")
        ).pack(pady=10)

        # 创建可用格式列表
        format_var = tk.StringVar()
        format_var.set(".md")  # 默认选择

        # 始终提供三种输出格式选项
        available_formats = [".md", ".docx", ".xlsx"]

        # 添加格式选择框
        for fmt in available_formats:
            ttk.Radiobutton(
                format_window,
                text=fmt.upper()[1:] + " 格式",
                variable=format_var,
                value=fmt
            ).pack(anchor=tk.W, padx=20, pady=5)

        # 确认按钮
        ttk.Button(
            format_window,
            text="确认",
            command=lambda: self.generate_document(values, format_var.get(), format_window)
        ).pack(pady=15)

    def generate_document(self, values, output_format, format_window):
        """生成最终文档"""
        # 关闭格式选择窗口
        format_window.destroy()

        # 请求保存位置
        output_file = filedialog.asksaveasfilename(
            title="保存文档",
            filetypes=[(f"{output_format.upper()[1:]} 文件", f"*{output_format}")],
            defaultextension=output_format
        )

        if not output_file:
            return

        # 替换占位符
        content = self.template_content
        for ph, value in values.items():
            placeholder = f'[{ph}]'
            content = content.replace(placeholder, value)

        try:
            # 根据不同格式保存文件
            if output_format == ".md":
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(content)

            elif output_format == ".docx":
                # 创建新Word文档
                doc = Document()
                # 按行分割内容并添加段落
                for line in content.split('\\n'):  # 修复：使用正确的换行符
                    doc.add_paragraph(line)
                doc.save(output_file)

            elif output_format == ".xlsx":
                # 创建Excel文件
                # 简单处理：将内容按行拆分，每行作为一行数据
                lines = [line.split('\\t') for line in content.split('\\n')]  # 修复：使用正确的换行符
                df = pd.DataFrame(lines)
                df.to_excel(output_file, index=False, header=False)

            messagebox.showinfo("成功", f"文档已成功保存至:\\n{output_file}")  # 修复：使用正确的换行符

        except Exception as e:
            messagebox.showerror("错误", f"保存文档失败: {str(e)}")

# 程序入口
def main():
    root = tk.Tk()
    app = DocumentGenerator(root)
    root.mainloop()

if __name__ == "__main__":
    main()
