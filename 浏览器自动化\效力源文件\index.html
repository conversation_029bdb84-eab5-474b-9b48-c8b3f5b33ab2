<!DOCTYPE html><html lang="zh-CN"><head>
        <meta charset="utf-8">
        <title>效力</title>
        <base href="">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link rel="icon" type="image/x-icon" href="https://firm.servever.cn/favicon.png">
        <link href="yibazhan/css/bootstrap.css" rel="stylesheet">
    <style>html,body{padding:0;margin:0;font-family:Helvetica,STHeiti,Microsoft YaHei,Verdana,Arial,Tahoma,sans-serif;vertical-align:baseline;border:0;box-sizing:border-box}body,html{height:100%}body{position:relative;min-width:1280px;font-family:Helvetica,STHeiti,Microsoft YaHei,Verdana,Arial,Tahoma,sans-serif;font-size:12px;line-height:1.5;color:#333;background-color:#f8f8f8}.seui_layout{position:relative;width:100%;height:100%}@media print{body{font-family:Georgia,Times New Roman,serif;color:#000}}</style><link rel="stylesheet" href="yibazhan/css/styles.d770d83de495476a.css" media="print" onload="this.media='all'"><noscript><link rel="stylesheet" href="yibazhan/css/styles.d770d83de495476a.css"></noscript></head>
    <body class="seui_layout" style="overflow-y: scroll">
        <seui-root></seui-root>
        <script>
            (function (a, b, c, d, e, j, s) {
                a[d] =
                    a[d] ||
                    function () {
                        (a[d].a = a[d].a || []).push(arguments);
                    };
                (j = b.createElement(c)), (s = b.getElementsByTagName(c)[0]);
                j.async = true;
                j.charset = 'UTF-8';
                j.src = 'https://static.meiqia.com/widget/loader.js';
                s.parentNode.insertBefore(j, s);
            })(window, document, 'script', '_MEIQIA');
            _MEIQIA('entId', 231297);
        </script>

        <!--设置localstorage配置对象-->
        <script>
            var NGXSTORE_CONFIG = {
                // 不要添加前缀，切换平台会造成重复添加前缀的问题
                prefix: '', // default: 'ngx_'
                serveverPrefix: 'ngx_d_p_', // default: 'ngx_'
            };
        </script>
    <script src="yibazhan/js/runtime.cef456c1d623c493.js" type="module"></script><script src="yibazhan/js/polyfills.9505878b3f316d49.js" type="module"></script><script src="yibazhan/js/scripts.ef29a23ec0ce48c5.js" defer></script><script src="yibazhan/js/vendor.533fa2a9e8fd915f.js" type="module"></script><script src="yibazhan/js/main.04c76d95aef36aad.js" type="module"></script>

</body></html>