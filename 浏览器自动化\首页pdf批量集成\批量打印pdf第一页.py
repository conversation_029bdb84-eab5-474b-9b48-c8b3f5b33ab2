import os
import sys
import re
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from pypdf import PdfReader, PdfWriter
import threading
import datetime

class PDFFirstPageExtractor:
    def __init__(self, root):
        self.root = root
        self.root.title("PDF首页提取工具")
        self.root.geometry("600x500")
        self.root.resizable(True, True)

        # 设置变量
        self.input_dir = tk.StringVar()
        self.output_dir = tk.StringVar()
        self.output_filename = tk.StringVar(value="首页合集.pdf")
        self.file_pattern = tk.StringVar(value="*.pdf")
        self.status_text = tk.StringVar(value="准备就绪")
        self.progress = tk.DoubleVar(value=0)

        # 创建界面
        self.create_widgets()

    def create_widgets(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 输入目录
        input_frame = ttk.LabelFrame(main_frame, text="输入目录", padding="5")
        input_frame.pack(fill=tk.X, pady=5)

        ttk.Entry(input_frame, textvariable=self.input_dir, width=50).pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        ttk.Button(input_frame, text="浏览...", command=self.browse_input_dir).pack(side=tk.RIGHT, padx=5)

        # 输出目录
        output_frame = ttk.LabelFrame(main_frame, text="输出目录", padding="5")
        output_frame.pack(fill=tk.X, pady=5)

        ttk.Entry(output_frame, textvariable=self.output_dir, width=50).pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        ttk.Button(output_frame, text="浏览...", command=self.browse_output_dir).pack(side=tk.RIGHT, padx=5)

        # 输出文件名
        filename_frame = ttk.LabelFrame(main_frame, text="输出文件名", padding="5")
        filename_frame.pack(fill=tk.X, pady=5)

        ttk.Entry(filename_frame, textvariable=self.output_filename).pack(fill=tk.X, padx=5)

        # 文件筛选规则
        filter_frame = ttk.LabelFrame(main_frame, text="PDF文件筛选规则", padding="5")
        filter_frame.pack(fill=tk.X, pady=5)

        ttk.Label(filter_frame, text="文件名模式:").pack(anchor=tk.W, padx=5)
        ttk.Entry(filter_frame, textvariable=self.file_pattern).pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(filter_frame, text="支持通配符: *.pdf, 2023*.pdf, 保单*.pdf 等").pack(anchor=tk.W, padx=5)

        # 操作按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)

        ttk.Button(button_frame, text="开始处理", command=self.start_processing).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="退出", command=self.root.quit).pack(side=tk.RIGHT, padx=5)

        # 进度条
        progress_frame = ttk.LabelFrame(main_frame, text="处理进度", padding="5")
        progress_frame.pack(fill=tk.X, pady=5)

        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress, maximum=100)
        self.progress_bar.pack(fill=tk.X, padx=5, pady=5)

        # 状态信息
        status_frame = ttk.LabelFrame(main_frame, text="状态信息", padding="5")
        status_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.log_text = tk.Text(status_frame, height=10, width=50, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        scrollbar = ttk.Scrollbar(self.log_text, command=self.log_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=scrollbar.set)

        # 状态栏
        status_bar = ttk.Label(self.root, textvariable=self.status_text, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def browse_input_dir(self):
        directory = filedialog.askdirectory(title="选择PDF文件所在目录")
        if directory:
            self.input_dir.set(directory)
            # 如果输出目录为空，默认设置为与输入相同
            if not self.output_dir.get():
                self.output_dir.set(directory)

    def browse_output_dir(self):
        directory = filedialog.askdirectory(title="选择输出目录")
        if directory:
            self.output_dir.set(directory)

    def log_message(self, message):
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def match_file_pattern(self, filename, pattern):
        # 将通配符模式转换为正则表达式
        regex_pattern = pattern.replace(".", "\\.").replace("*", ".*").replace("?", ".")
        return re.match(f"^{regex_pattern}$", filename, re.IGNORECASE) is not None

    def process_pdfs(self):
        input_dir = self.input_dir.get()
        output_dir = self.output_dir.get()
        output_filename = self.output_filename.get()
        pattern = self.file_pattern.get()

        if not input_dir or not os.path.isdir(input_dir):
            messagebox.showerror("错误", "请选择有效的输入目录")
            return

        if not output_dir or not os.path.isdir(output_dir):
            messagebox.showerror("错误", "请选择有效的输出目录")
            return

        if not output_filename:
            messagebox.showerror("错误", "请输入有效的输出文件名")
            return

        # 确保输出文件名以.pdf结尾
        if not output_filename.lower().endswith('.pdf'):
            output_filename += '.pdf'

        self.status_text.set("正在处理...")
        self.log_message(f"开始处理 - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.log_message(f"输入目录: {input_dir}")
        self.log_message(f"输出目录: {output_dir}")
        self.log_message(f"文件筛选: {pattern}")

        try:
            # 获取所有PDF文件
            all_files = [f for f in os.listdir(input_dir) if f.lower().endswith('.pdf')]

            # 根据模式筛选文件
            if pattern and pattern != "*.pdf":
                filtered_files = []
                for file in all_files:
                    if self.match_file_pattern(file, pattern):
                        filtered_files.append(file)
                self.log_message(f"根据模式 '{pattern}' 筛选出 {len(filtered_files)}/{len(all_files)} 个文件")
                pdf_files = filtered_files
            else:
                pdf_files = all_files

            if not pdf_files:
                self.log_message("没有找到匹配的PDF文件")
                self.status_text.set("完成 - 没有找到匹配的文件")
                return

            # 创建PDF写入器
            output = PdfWriter()
            total_files = len(pdf_files)
            processed = 0

            for file in pdf_files:
                file_path = os.path.join(input_dir, file)
                try:
                    reader = PdfReader(file_path, strict=True)
                    if len(reader.pages) > 0:
                        output.add_page(reader.pages[0])  # 仅添加第一页
                        self.log_message(f"成功添加 {file} 的第一页")
                    else:
                        self.log_message(f"警告：{file} 没有可提取的页面")
                except Exception as e:
                    self.log_message(f"错误处理 {file}: {str(e)}")

                processed += 1
                self.progress.set((processed / total_files) * 100)
                self.root.update_idletasks()

            # 保存输出文件
            output_path = os.path.join(output_dir, output_filename)
            with open(output_path, "wb") as output_file:
                output.write(output_file)

            self.log_message(f"处理完成！输出文件保存为: {output_path}")
            self.status_text.set(f"完成 - 已处理 {processed} 个文件")
            messagebox.showinfo("完成", f"已成功处理 {processed} 个PDF文件\n输出文件: {output_path}")

        except Exception as e:
            self.log_message(f"处理过程中发生错误: {str(e)}")
            self.status_text.set("错误 - 处理失败")
            messagebox.showerror("错误", f"处理过程中发生错误:\n{str(e)}")

        finally:
            self.progress.set(100)

    def start_processing(self):
        # 使用线程处理，避免界面冻结
        processing_thread = threading.Thread(target=self.process_pdfs)
        processing_thread.daemon = True
        processing_thread.start()

def main():
    root = tk.Tk()
    app = PDFFirstPageExtractor(root)
    root.mainloop()

if __name__ == "__main__":
    main()