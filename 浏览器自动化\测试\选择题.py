import tkinter as tk
from tkinter import messagebox

class QuizApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("选择题测试")
        self.root.geometry("1200x800")

        # 初始化变量
        self.questions = {}
        self.questions_list = []
        self.current_question_index = 0
        self.score = 0
        self.var = tk.StringVar()
        self.options = []

        # 加载题目
        self.load_questions()

        # 创建GUI
        self.create_gui()

        # 开始第一题
        if self.questions_list:
            self.show_question()

    def load_questions(self):
        """加载题目数据"""
        # 使用内置示例题目
        self.load_sample_questions()

        # 转换为列表便于索引
        self.questions_list = list(self.questions.items())

    def load_sample_questions(self):
        """加载示例题目"""
        self.questions = {
            "Python中哪个关键字用于定义函数？":
                (["def", "function", "func", "define"], "A"),
            "以下哪个是Python的数据类型？":
                (["int", "string", "boolean", "以上都是"], "D"),
            "Python中如何创建列表？":
                (["[]", "{}", "()", "以上都不对"], "A"),
            "Python中哪个方法用于向列表添加元素？":
                (["add()", "append()", "insert()", "push()"], "B"),
            "Python中如何获取字符串长度？":
                (["length()", "size()", "len()", "count()"], "C")
        }

    def create_gui(self):
        """创建图形界面"""
        # 题目显示区域
        self.question_frame = tk.Frame(self.root)
        self.question_frame.pack(pady=20, padx=20, fill="x")

        self.question_label = tk.Label(
            self.question_frame,
            text="",
            font=("Arial", 12),
            wraplength=500,
            justify="left"
        )
        self.question_label.pack()

        # 选项区域
        self.options_frame = tk.Frame(self.root)
        self.options_frame.pack(pady=10, padx=20, fill="x")

        # 创建4个选项按钮
        for i in range(4):
            rb = tk.Radiobutton(
                self.options_frame,
                variable=self.var,
                value=chr(65 + i),  # A, B, C, D
                font=("Arial", 10),
                anchor="w",
                wraplength=450
            )
            rb.pack(anchor="w", pady=2)
            self.options.append(rb)

        # 按钮区域
        self.button_frame = tk.Frame(self.root)
        self.button_frame.pack(pady=20)

        self.next_button = tk.Button(
            self.button_frame,
            text="提交答案",
            command=self.check_answer,
            font=("Arial", 10),
            bg="#4CAF50",
            fg="white",
            padx=20
        )
        self.next_button.pack(side="left", padx=10)

        # 进度显示
        self.progress_label = tk.Label(
            self.root,
            text="",
            font=("Arial", 9),
            fg="gray"
        )
        self.progress_label.pack(pady=10)

    def show_question(self):
        """显示当前题目"""
        if self.current_question_index < len(self.questions_list):
            question, (opts, _) = self.questions_list[self.current_question_index]

            # 显示题目
            self.question_label.config(text=f"题目 {self.current_question_index + 1}: {question}")

            # 显示选项
            for i, option in enumerate(opts):
                self.options[i].config(text=f"{chr(65 + i)}. {option}")

            # 清除之前的选择
            self.var.set("")

            # 更新进度
            self.progress_label.config(
                text=f"进度: {self.current_question_index + 1}/{len(self.questions_list)} | 当前得分: {self.score}"
            )
        else:
            self.show_result()

    def check_answer(self):
        """检查答案并进入下一题"""
        selected_answer = self.var.get()

        if not selected_answer:
            messagebox.showwarning("提示", "请选择一个答案！")
            return

        # 获取当前题目的正确答案
        _, (_, correct_answer) = self.questions_list[self.current_question_index]

        # 检查答案
        if selected_answer == correct_answer:
            self.score += 1
            messagebox.showinfo("正确", "回答正确！")
        else:
            messagebox.showinfo("错误", f"回答错误！正确答案是: {correct_answer}")

        # 进入下一题
        self.current_question_index += 1
        self.show_question()

    def show_result(self):
        """显示最终结果"""
        total_questions = len(self.questions_list)
        percentage = (self.score / total_questions) * 100 if total_questions > 0 else 0

        result_message = f"""
测试完成！

总题数: {total_questions}
正确答案: {self.score}
错误答案: {total_questions - self.score}
正确率: {percentage:.1f}%

感谢参与测试！
        """

        messagebox.showinfo("测试结果", result_message)
        self.root.quit()

    def run(self):
        """运行应用"""
        if not self.questions_list:
            messagebox.showerror("错误", "没有可用的题目！")
            return

        self.root.mainloop()

def main():
    """主函数"""
    app = QuizApp()
    app.run()

if __name__ == "__main__":
    main()