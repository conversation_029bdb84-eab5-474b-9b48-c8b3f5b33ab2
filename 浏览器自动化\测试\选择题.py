import tkinter as tk
from tkinter import messagebox
root = tk.Tk()
root.title("选择题")
question_label = tk.Label(root, text="")
question_label.pack()
var = tk.StringVar()
options = []
for i in range(4):
    rb = tk.Radiobutton(root, variable=var, value=chr(65 + i))
    rb.pack(anchor="w")
    options.append(rb)
def next_question():
    # 逻辑代码
    pass
next_button = tk.Button(root, text="下一题", command=next_question)
next_button.pack()
root.mainloop()

questions = load_questions('questions.csv')




questions_iterator = iter(questions.items())




score = 0




def next_question():




    global score




    try:




        question, (opts, correct_answer) = next(questions_iterator)




        question_label.config(text=question)




        for i, option in enumerate(opts):




            options[i].config(text=option)




        var.set(None)




    except StopIteration:




        messagebox.showinfo("完成", f"你的总分是：{score}/{len(questions)}")




        root.quit()




def check_answer():




    global score




    selected_answer = var.get()




    question, (opts, correct_answer) = list(questions.items())[len(questions) - len(questions_iterator) - 1]




    if selected_answer == correct_answer:




        score += 1




    next_question()




next_button.config(command=check_answer)




next_question()