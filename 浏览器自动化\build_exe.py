import os
import sys
import subprocess
import shutil

def check_dependencies():
    """检查并安装所需的依赖"""
    dependencies = [
        {"name": "pypdf", "import_name": "pypdf"},
        {"name": "pyinstaller", "import_name": "PyInstaller"}
    ]

    all_installed = True

    for dep in dependencies:
        try:
            __import__(dep["import_name"])
            print(f"{dep['name']} 已安装")
        except ImportError:
            print(f"{dep['name']} 未安装，正在安装...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", dep["name"]])
                print(f"{dep['name']} 安装成功")
            except Exception as e:
                print(f"安装 {dep['name']} 失败: {e}")
                all_installed = False

    return all_installed

def build_executable():
    """编译脚本为可执行文件"""
    script_path = "批量打印pdf第一页.py"

    if not os.path.exists(script_path):
        print(f"错误: 找不到脚本文件 {script_path}")
        return False

    print(f"开始编译 {script_path}...")

    # 确保dist和build目录不存在
    for dir_name in ["dist", "build"]:
        if os.path.exists(dir_name):
            print(f"删除旧的 {dir_name} 目录...")
            shutil.rmtree(dir_name)

    # 编译命令
    cmd = [
        "pyinstaller",
        "--noconfirm",
        "--onefile",
        "--windowed",
        "--name", "PDF首页提取工具",
        "--icon", "NONE",  # 可以替换为实际图标路径
        script_path
    ]

    try:
        subprocess.check_call(cmd)
        print("编译成功!")

        # 移动可执行文件到当前目录
        exe_path = os.path.join("dist", "PDF首页提取工具.exe")
        if os.path.exists(exe_path):
            shutil.copy(exe_path, ".")
            print(f"可执行文件已复制到当前目录: PDF首页提取工具.exe")

        return True
    except Exception as e:
        print(f"编译失败: {e}")
        return False

if __name__ == "__main__":
    print("PDF首页提取工具 - 编译脚本")
    print("-" * 50)

    if check_dependencies():
        success = build_executable()
        if success:
            print("\n编译完成! 按任意键退出...")
        else:
            print("\n编译失败! 按任意键退出...")
    else:
        print("\n依赖安装失败，编译失败! 按任意键退出...")

    input()
