import os
import re
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from pypdf import PdfReader, PdfWriter
import threading
import datetime
import logging
import logging.handlers

# 设置日志
def setup_logger():
    """设置日志系统"""
    logger = logging.getLogger("PDFCustomPrint")
    logger.setLevel(logging.DEBUG)

    # 创建日志目录
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    os.makedirs(log_dir, exist_ok=True)

    # 文件处理器 - 按日期滚动
    log_file = os.path.join(log_dir, "pdf_custom_print.log")
    file_handler = logging.handlers.TimedRotatingFileHandler(
        log_file, when='midnight', backupCount=7, encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # 设置格式
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

# 创建全局日志对象
logger = setup_logger()

class PDFCustomPrinter:
    def __init__(self, root):
        self.root = root
        self.root.title("自定义PDF打印")
        self.root.geometry("600x500")
        self.root.resizable(True, True)

        logger.info("应用程序启动")

        # 设置变量
        self.input_dir = tk.StringVar()
        self.output_dir = tk.StringVar()
        self.output_filename = tk.StringVar(value="自定义打印.pdf")
        self.file_pattern = tk.StringVar(value="*.pdf")
        self.status_text = tk.StringVar(value="准备就绪")
        self.progress = tk.DoubleVar(value=0)

        # 创建界面
        self.create_widgets()

    def create_widgets(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 输入文件
        input_frame = ttk.LabelFrame(main_frame, text="输入文件", padding="5")
        input_frame.pack(fill=tk.X, pady=5)

        ttk.Entry(input_frame, textvariable=self.input_dir, width=50).pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        ttk.Button(input_frame, text="浏览...", command=self.browse_input_dir).pack(side=tk.RIGHT, padx=5)

        # 输出目录
        output_frame = ttk.LabelFrame(main_frame, text="输出目录", padding="5")
        output_frame.pack(fill=tk.X, pady=5)

        ttk.Entry(output_frame, textvariable=self.output_dir, width=50).pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        ttk.Button(output_frame, text="浏览...", command=self.browse_output_dir).pack(side=tk.RIGHT, padx=5)

        # 输出文件名
        filename_frame = ttk.LabelFrame(main_frame, text="输出文件名", padding="5")
        filename_frame.pack(fill=tk.X, pady=5)

        ttk.Entry(filename_frame, textvariable=self.output_filename).pack(fill=tk.X, padx=5)

        # 页码选择规则
        page_frame = ttk.LabelFrame(main_frame, text="页码选择规则", padding="5")
        page_frame.pack(fill=tk.X, pady=5)

        ttk.Label(page_frame, text="页码范围:").pack(anchor=tk.W, padx=5)
        self.page_range = tk.StringVar(value="1")  # 默认提取第一页
        ttk.Entry(page_frame, textvariable=self.page_range).pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(page_frame, text="输入页码范围如 1-3,5 或 1,3,5").pack(anchor=tk.W, padx=5)

        # 文件筛选规则
        filter_frame = ttk.LabelFrame(main_frame, text="PDF文件筛选规则", padding="5")
        filter_frame.pack(fill=tk.X, pady=5)

        ttk.Label(filter_frame, text="文件名模式:").pack(anchor=tk.W, padx=5)
        ttk.Entry(filter_frame, textvariable=self.file_pattern).pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(filter_frame, text="支持通配符: *.pdf, 2023*.pdf, 保单*.pdf 等").pack(anchor=tk.W, padx=5)

        # 操作按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)

        ttk.Button(button_frame, text="开始处理", command=self.start_processing).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="退出", command=self.root.quit).pack(side=tk.RIGHT, padx=5)

        # 进度条
        progress_frame = ttk.LabelFrame(main_frame, text="处理进度", padding="5")
        progress_frame.pack(fill=tk.X, pady=5)

        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress, maximum=100)
        self.progress_bar.pack(fill=tk.X, padx=5, pady=5)

        # 状态信息
        status_frame = ttk.LabelFrame(main_frame, text="状态信息", padding="5")
        status_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.log_text = tk.Text(status_frame, height=10, width=50, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        scrollbar = ttk.Scrollbar(self.log_text, command=self.log_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=scrollbar.set)

        # 状态栏
        status_bar = ttk.Label(self.root, textvariable=self.status_text, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def browse_input_dir(self):
        files = filedialog.askopenfilenames(title="选择PDF文件", filetypes=[("PDF files", "*.pdf")])
        if files:
            self.input_dir.set(','.join(files))
            # 如果输出目录为空，默认设置为第一个文件所在目录
            if not self.output_dir.get():
                first_file_dir = os.path.dirname(files[0])
                self.output_dir.set(first_file_dir)

    def browse_output_dir(self):
        directory = filedialog.askdirectory(title="选择输出目录")
        if directory:
            self.output_dir.set(directory)

    def log_message(self, message, level='info'):
        """记录消息到GUI日志框和日志文件"""
        # 添加到GUI
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

        # 记录到日志文件
        if level.lower() == 'debug':
            logger.debug(message)
        elif level.lower() == 'warning':
            logger.warning(message)
        elif level.lower() == 'error':
            logger.error(message)
        else:
            logger.info(message)

    def match_file_pattern(self, filename, pattern):
        # 将通配符模式转换为正则表达式
        regex_pattern = pattern.replace(".", "\\.").replace("*", ".*").replace("?", ".")
        return re.match(f"^{regex_pattern}$", filename, re.IGNORECASE) is not None

    def process_pdfs(self):
        """处理PDF文件，提取指定页码并合并"""
        input_files = self.input_dir.get().split(',')
        input_files = [f.strip() for f in input_files]
        output_dir = self.output_dir.get()
        output_filename = self.output_filename.get()
        pattern = self.file_pattern.get()
        page_range = self.page_range.get()

        # 输入验证
        if not input_files or not all(os.path.isfile(f) for f in input_files):
            error_msg = "请选择有效的PDF文件"
            self.log_message(error_msg, 'error')
            messagebox.showerror("错误", error_msg)
            return

        if not output_dir or not os.path.isdir(output_dir):
            error_msg = "请选择有效的输出目录"
            self.log_message(error_msg, 'error')
            messagebox.showerror("错误", error_msg)
            return

        if not output_filename:
            error_msg = "请输入有效的输出文件名"
            self.log_message(error_msg, 'error')
            messagebox.showerror("错误", error_msg)
            return

        # 确保输出文件名以.pdf结尾
        if not output_filename.lower().endswith('.pdf'):
            output_filename += '.pdf'
            self.output_filename.set(output_filename)

        # 更新状态
        self.status_text.set("正在处理...")
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.log_message(f"=== 开始处理 - {current_time} ===")
        self.log_message(f"输入文件: {len(input_files)} 个文件")
        self.log_message(f"输出目录: {output_dir}")
        self.log_message(f"页码范围: {page_range}")
        self.log_message(f"文件筛选: {pattern}")

        logger.debug(f"详细输入文件列表: {', '.join(input_files)}")

        try:
            # 应用筛选规则
            if pattern and pattern != "*.pdf":
                filtered_files = [f for f in input_files if self.match_file_pattern(os.path.basename(f), pattern)]
                self.log_message(f"根据模式 '{pattern}' 筛选出 {len(filtered_files)}/{len(input_files)} 个文件")
            else:
                filtered_files = input_files

            if not filtered_files:
                self.log_message("没有找到匹配的PDF文件", 'warning')
                self.status_text.set("完成 - 没有找到匹配的文件")
                return

            # 创建PDF写入器
            output = PdfWriter()
            total_files = len(filtered_files)
            processed = 0
            total_pages_added = 0

            for file in filtered_files:
                try:
                    reader = PdfReader(file, strict=True)
                    file_basename = os.path.basename(file)

                    # 解析页码范围
                    pages_to_extract = self.parse_page_range(page_range, file)

                    if pages_to_extract:
                        pages_added = 0
                        for page_num in pages_to_extract:
                            if 0 <= page_num < len(reader.pages):
                                output.add_page(reader.pages[page_num])
                                self.log_message(f"添加 {file_basename} 的第 {page_num+1} 页")
                                pages_added += 1
                                total_pages_added += 1
                            else:
                                self.log_message(f"警告：{file_basename} 没有第 {page_num+1} 页", 'warning')

                        if pages_added > 0:
                            self.log_message(f"成功从 {file_basename} 添加了 {pages_added} 页")
                        else:
                            self.log_message(f"警告：未能从 {file_basename} 添加任何页面", 'warning')
                    else:
                        self.log_message(f"警告：{file_basename} 页码范围无效", 'warning')
                except Exception as e:
                    error_msg = f"错误处理 {os.path.basename(file)}: {str(e)}"
                    self.log_message(error_msg, 'error')
                    logger.exception(f"处理文件 {file} 时出错")

                processed += 1
                self.progress.set((processed / total_files) * 100)
                self.root.update_idletasks()

            # 检查是否有页面被添加
            if total_pages_added == 0:
                self.log_message("警告：没有任何页面被添加到输出文件", 'warning')
                self.status_text.set("完成 - 没有页面被添加")
                messagebox.showwarning("警告", "没有任何页面被添加到输出文件")
                return

            # 保存输出文件
            output_path = os.path.join(output_dir, output_filename)
            with open(output_path, "wb") as output_file:
                output.write(output_file)

            success_msg = f"处理完成！已添加 {total_pages_added} 页到输出文件: {output_path}"
            self.log_message(success_msg)
            self.status_text.set(f"完成 - 已处理 {processed} 个文件，添加 {total_pages_added} 页")
            messagebox.showinfo("完成", f"已成功处理 {processed} 个PDF文件\n共添加 {total_pages_added} 页\n输出文件: {output_path}")

        except Exception as e:
            error_msg = f"处理过程中发生错误: {str(e)}"
            self.log_message(error_msg, 'error')
            logger.exception("PDF处理过程中发生异常")
            self.status_text.set("错误 - 处理失败")
            messagebox.showerror("错误", f"处理过程中发生错误:\n{str(e)}")

        finally:
            self.progress.set(100)
            logger.info(f"处理任务结束 - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    def parse_page_range(self, page_range_str, file_path):
        """解析页码范围字符串，返回页码列表（0-based索引）"""
        try:
            if not page_range_str or page_range_str.strip() == "":
                logger.warning(f"页码范围为空，默认使用第一页: {file_path}")
                return [0]  # 默认返回第一页

            # 获取PDF总页数
            reader = PdfReader(file_path)
            total_pages = len(reader.pages)
            logger.debug(f"PDF文件 {os.path.basename(file_path)} 共有 {total_pages} 页")

            result = []
            # 分割逗号分隔的部分
            parts = page_range_str.split(',')

            for part in parts:
                part = part.strip()
                if not part:
                    continue

                # 处理范围 (例如 "1-5")
                if '-' in part:
                    try:
                        start, end = map(int, part.split('-'))
                        # 转换为0-based索引
                        start_idx = start - 1
                        end_idx = end - 1

                        # 验证范围
                        if start_idx < 0:
                            logger.warning(f"页码范围起始值 {start} 无效，使用1")
                            start_idx = 0
                        if end_idx >= total_pages:
                            logger.warning(f"页码范围结束值 {end} 超出PDF总页数 {total_pages}，使用最大页码")
                            end_idx = total_pages - 1

                        # 添加范围内的所有页码
                        result.extend(range(start_idx, end_idx + 1))
                    except ValueError:
                        logger.error(f"无效的页码范围: {part}")
                        continue
                else:
                    # 处理单个页码
                    try:
                        page = int(part)
                        # 转换为0-based索引
                        page_idx = page - 1

                        # 验证页码
                        if 0 <= page_idx < total_pages:
                            result.append(page_idx)
                        else:
                            logger.warning(f"页码 {page} 超出范围 (1-{total_pages})")
                    except ValueError:
                        logger.error(f"无效的页码: {part}")
                        continue

            # 去重并排序
            result = sorted(set(result))

            if not result:
                logger.warning(f"没有有效的页码，默认使用第一页: {file_path}")
                return [0]  # 如果没有有效页码，默认返回第一页

            logger.info(f"解析页码范围 '{page_range_str}' 结果: {[p+1 for p in result]}")
            return result

        except Exception as e:
            logger.error(f"解析页码范围时出错: {str(e)}")
            return [0]  # 出错时默认返回第一页

    def start_processing(self):
        # 使用线程处理，避免界面冻结
        logger.info("开始处理任务")
        processing_thread = threading.Thread(target=self.process_pdfs)
        processing_thread.daemon = True
        processing_thread.start()

def main():
    root = tk.Tk()
    app = PDFCustomPrinter(root)
    root.mainloop()

if __name__ == "__main__":
    main()
