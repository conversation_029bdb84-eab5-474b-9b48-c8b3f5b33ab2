```markdown
# 效力平台Chrome插件开发指南（Windows/VSCode版）

## 开发环境配置

### 1. 系统要求
- Windows 10/11 64位
- Chrome浏览器 100+版本
- [VSCode 1.85+](https://code.visualstudio.com/)

### 2. 基础环境安装
```powershell
# 安装Python 3.10（勾选Add to PATH）
winget install Python.Python.3.10

# 安装必要工具
winget install Google.Chrome
winget install Git.Git
```

### 3. VSCode插件安装
1. 打开扩展商店(Ctrl+Shift+X)，安装：
   - **Python** (Microsoft官方插件)
   - **Jupyter** (代码片段测试)
   - **Chrome Debugger** (调试插件)
   - **Live Server** (本地资源加载)

### 4. Python环境配置
```bash
# 创建虚拟环境
python -m venv .venv
.\.venv\Scripts\activate

# 安装核心依赖
pip install selenium cryptography pytest-html chromedriver-autoinstaller
```

## 项目结构搭建
```
效力插件/
├── src/
│   ├── manifest.json       # 插件配置文件
│   ├── background.js       # 后台任务脚本
│   ├── content_scripts/    
│   │   ├── login.js        # 登录处理逻辑
│   │   └── checker.js      # 检查器逻辑
├── utils/
│   ├── crypto_util.py      # AES加密模块
│   └── report_gen.py       # PDF报告生成
├── tests/
│   ├── e2e/                # 端到端测试
│   └── unit/               # 单元测试
└── .vscode/                # 编辑器配置
    ├── settings.json
    └── launch.json
```

## 代码开发步骤

### 1. 配置manifest.json
```json
{
  "manifest_version": 3,
  "name": "效力平台检查器",
  "version": "1.0",
  "permissions": ["storage", "activeTab", "scripting"],
  "background": {
    "service_worker": "background.js"
  },
  "content_scripts": [{
    "matches": ["https://firm.servever.cn/*"],
    "js": ["content_scripts/login.js"]
  }]
}
```

### 2. 凭证注入逻辑（login.js）
```javascript
// 元素选择器配置
const SELECTORS = {
  username: 'input[data-testid="login-username"]',
  password: 'input[data-testid="login-pwd"]',
  submit: 'button.login-btn'
};

// 与Python模块交互
async function getCredentials() {
  const res = await fetch('http://localhost:5000/api/credentials');
  return await res.json();
}

// 自动填充逻辑
document.addEventListener('DOMContentLoaded', async () => {
  const { username, encryptedPwd } = await getCredentials();
  document.querySelector(SELECTORS.username).value = username;
  document.querySelector(SELECTORS.password).value = decrypt(encryptedPwd);
  document.querySelector(SELECTORS.submit).click();
});
```

### 3. Python加密模块（crypto_util.py）
```python
from cryptography.fernet import Fernet

class CredentialManager:
    def __init__(self):
        self.key = Fernet.generate_key()
        self.cipher = Fernet(self.key)
    
    def encrypt(self, text: str) -> bytes:
        return self.cipher.encrypt(text.encode())
    
    def decrypt(self, token: bytes) -> str:
        return self.cipher.decrypt(token).decode()
```

## 调试与测试

### 1. VSCode调试配置（.vscode/launch.json）
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "调试内容脚本",
      "type": "chrome",
      "request": "launch",
      "url": "https://firm.servever.cn",
      "webRoot": "${workspaceFolder}/src"
    },
    {
      "name": "运行Python测试",
      "type": "python",
      "request": "launch",
      "program": "-m pytest",
      "args": ["tests/"]
    }
  ]
}
```

### 2. 执行端到端测试
```bash
# 启动测试服务
python -m http.server 5000 --directory src

# 运行测试套件
pytest tests/e2e/test_login_flow.py --html=report.html
```

## 插件打包部署

1. 在VSCode资源管理器中右键点击`src`文件夹
2. 选择「压缩为ZIP」生成`效力插件.zip`
3. 在Chrome地址栏输入：
   ```
   chrome://extensions/
   ```
4. 开启「开发者模式」后点击「加载已解压的扩展程序」

## 注意事项

1. **驱动管理**
   - 在Python代码开头添加：
   ```python
   import chromedriver_autoinstaller
   chromedriver_autoinstaller.install()
   ```

2. **依赖冲突处理**
   ```bash
   pip install pipenv
   pipenv graph  # 可视化依赖树
   ```

3. **企业代理设置**
   ```powershell
   # 配置git代理
   git config --global http.proxy http://corp-proxy:8080
   ```

## 常见问题解决

❌ **元素定位失败**  
✅ 解决方案：在VSCode中安装「Selector Gadget」插件辅助定位CSS选择器

❌ **跨域请求被拦截**  
✅ 在manifest.json添加权限声明：
```json
"host_permissions": ["*://firm.servever.cn/*"]
```

❌ **ChromeDriver版本不匹配**  
✅ 运行以下命令自动更新：
```python
from selenium import webdriver
driver = webdriver.Chrome()
```

请将本文件保存为`setup_guide.md`，配合代码仓库中的示例项目使用。
```